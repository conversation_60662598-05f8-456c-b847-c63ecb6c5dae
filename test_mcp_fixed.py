#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复后的MCP测试脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_mcp_service():
    """测试MCP服务"""
    print("🧪 测试修复后的MCP服务...")
    
    try:
        from app.models.mcp import MCPServerConfig, MCPTransportType
        from app.services.mcp_service import MCPService
        
        # 创建MCP服务
        mcp_service = MCPService()
        
        # 创建一个简单的测试配置（禁用状态）
        test_config = MCPServerConfig(
            name="test-server",
            description="测试服务器",
            transport_type=MCPTransportType.STDIO,
            command="echo",
            args=["hello"],
            enabled=False  # 禁用以避免实际连接
        )
        
        # 添加服务器
        await mcp_service.add_server(test_config)
        print("✅ 成功添加测试服务器")
        
        # 获取状态
        status = mcp_service.get_server_status()
        print(f"✅ 服务器状态获取成功，共 {len(status)} 个服务器")
        
        # 清理
        await mcp_service.shutdown()
        print("✅ 服务清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_config_loading():
    """测试配置加载"""
    print("\n⚙️  测试配置加载...")
    
    try:
        from app.core.config import get_mcp_config
        
        config = get_mcp_config()
        print(f"✅ 配置加载成功")
        print(f"   服务器数量: {len(config.servers)}")
        print(f"   全局超时: {config.global_timeout}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 MCP修复验证测试")
    print("=" * 40)
    
    tests = [
        ("配置加载", test_config_loading),
        ("MCP服务", test_mcp_service),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 运行测试: {test_name}")
            if await test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 出错: {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复成功")
        return 0
    else:
        print("⚠️  部分测试失败")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
