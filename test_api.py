#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本
"""

import requests
import json
import time

BASE_URL = "http://localhost:8002/api/v1"

def test_health():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    try:
        response = requests.get("http://localhost:8002/health", timeout=5)
        if response.status_code == 200:
            print("✅ 健康检查通过")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查出错: {e}")
        return False

def test_mcp_status():
    """测试MCP状态"""
    print("\n🔧 测试MCP状态...")
    try:
        response = requests.get(f"{BASE_URL}/mcp/status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ MCP状态获取成功")
            print(f"   启用状态: {'是' if data.get('enabled') else '否'}")
            print(f"   服务器数量: {len(data.get('servers', []))}")
            print(f"   工具数量: {len(data.get('tools', []))}")
            return True
        else:
            print(f"❌ MCP状态获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ MCP状态获取出错: {e}")
        return False

def test_simple_chat():
    """测试简单聊天"""
    print("\n💬 测试简单聊天...")
    try:
        payload = {"message": "你好"}
        response = requests.post(
            f"{BASE_URL}/chat", 
            json=payload, 
            timeout=30
        )
        if response.status_code == 200:
            data = response.json()
            print("✅ 聊天测试成功")
            print(f"   AI回复: {data.get('message', '')[:100]}...")
            print(f"   对话ID: {data.get('conversation_id', '')}")
            return True
        else:
            print(f"❌ 聊天测试失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 聊天测试出错: {e}")
        return False

def test_mcp_config():
    """测试MCP配置"""
    print("\n⚙️  测试MCP配置...")
    try:
        response = requests.get(f"{BASE_URL}/mcp/config", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ MCP配置获取成功")
            print(f"   全局超时: {data.get('global_timeout', 'N/A')}秒")
            print(f"   自动重连: {'是' if data.get('auto_reconnect') else '否'}")
            servers = data.get('servers', [])
            print(f"   配置的服务器: {len(servers)}")
            for server in servers[:3]:  # 只显示前3个
                status = "启用" if server.get('enabled') else "禁用"
                print(f"     - {server.get('name')}: {status}")
            return True
        else:
            print(f"❌ MCP配置获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ MCP配置获取出错: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 AI助手API测试")
    print("=" * 40)
    
    tests = [
        ("健康检查", test_health),
        ("MCP状态", test_mcp_status),
        ("MCP配置", test_mcp_config),
        ("简单聊天", test_simple_chat),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            time.sleep(1)  # 间隔1秒
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n📚 接下来你可以:")
        print("   - 访问 http://localhost:8001/docs 查看API文档")
        print("   - 配置MCP服务器启用更多功能")
        print("   - 通过API与AI助手对话")
    else:
        print("⚠️  部分测试失败，请检查服务状态")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    exit(main())
