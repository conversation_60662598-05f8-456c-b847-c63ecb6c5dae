import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from contextlib import asynccontextmanager

from app.models.mcp import (
    MCPServerConfig, MCPTool, MCPResource, MCPPrompt,
    MCPToolCall, MCPToolResult, MCPServerStatus, MCPTransportType
)

logger = logging.getLogger(__name__)


class MCPClient:
    """单个MCP服务器的客户端"""
    
    def __init__(self, config: MCPServerConfig):
        self.config = config
        self.session: Optional[ClientSession] = None
        self.connected = False
        self.tools: Dict[str, MCPTool] = {}
        self.resources: Dict[str, MCPResource] = {}
        self.prompts: Dict[str, MCPPrompt] = {}
        self._stdio_context = None
        
    async def connect(self):
        """连接到MCP服务器"""
        try:
            if self.config.transport_type == MCPTransportType.STDIO:
                await self._connect_stdio()
            elif self.config.transport_type == MCPTransportType.SSE:
                await self._connect_sse()
            else:
                raise ValueError(f"不支持的传输类型: {self.config.transport_type}")

            # 验证连接
            if self.session:
                await self._discover_capabilities()
                self.connected = True
                logger.info(f"成功连接到MCP服务器: {self.config.name}")
            else:
                raise RuntimeError("会话创建失败")

        except Exception as e:
            logger.error(f"连接MCP服务器失败 {self.config.name}: {e}")
            self.connected = False
            # 清理资源
            await self.disconnect()
            raise
    
    async def _connect_stdio(self):
        """通过stdio连接"""
        if not self.config.command:
            raise ValueError("stdio传输需要指定command")

        server_params = StdioServerParameters(
            command=self.config.command,
            args=self.config.args or [],
            env=self.config.env or {}
        )

        # 创建stdio客户端上下文管理器
        self._stdio_context = stdio_client(server_params)
        # 进入上下文并获取流
        read_stream, write_stream = await self._stdio_context.__aenter__()

        # 创建ClientSession
        self.session = ClientSession(read_stream, write_stream)
        # 初始化会话
        await self.session.initialize()
    
    async def _connect_sse(self):
        """通过SSE连接"""
        # TODO: 实现SSE连接
        raise NotImplementedError("SSE传输暂未实现")
    
    async def _discover_capabilities(self):
        """发现服务器能力"""
        if not self.session:
            return

        try:
            # 获取工具列表
            try:
                tools_result = await self.session.list_tools()
                if hasattr(tools_result, 'tools'):
                    for tool in tools_result.tools:
                        mcp_tool = MCPTool(
                            name=tool.name,
                            description=tool.description,
                            input_schema=tool.inputSchema,
                            server_name=self.config.name
                        )
                        self.tools[tool.name] = mcp_tool
                        logger.info(f"发现工具: {tool.name}")
            except Exception as e:
                logger.warning(f"获取工具列表失败: {e}")

            # 获取资源列表
            try:
                resources_result = await self.session.list_resources()
                if hasattr(resources_result, 'resources'):
                    for resource in resources_result.resources:
                        mcp_resource = MCPResource(
                            uri=resource.uri,
                            name=resource.name,
                            description=getattr(resource, 'description', ''),
                            mime_type=getattr(resource, 'mimeType', None),
                            server_name=self.config.name
                        )
                        self.resources[resource.uri] = mcp_resource
                        logger.info(f"发现资源: {resource.name}")
            except Exception as e:
                logger.warning(f"获取资源列表失败: {e}")

            # 获取提示模板列表
            try:
                prompts_result = await self.session.list_prompts()
                if hasattr(prompts_result, 'prompts'):
                    for prompt in prompts_result.prompts:
                        mcp_prompt = MCPPrompt(
                            name=prompt.name,
                            description=prompt.description,
                            arguments=getattr(prompt, 'arguments', None),
                            server_name=self.config.name
                        )
                        self.prompts[prompt.name] = mcp_prompt
                        logger.info(f"发现提示模板: {prompt.name}")
            except Exception as e:
                logger.warning(f"获取提示模板列表失败: {e}")

        except Exception as e:
            logger.error(f"发现服务器能力失败: {e}")
            logger.exception("详细错误信息:")
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> MCPToolResult:
        """调用工具"""
        if not self.connected or not self.session:
            return MCPToolResult(
                success=False,
                content="",
                error="服务器未连接",
                tool_name=tool_name,
                server_name=self.config.name
            )
        
        if tool_name not in self.tools:
            return MCPToolResult(
                success=False,
                content="",
                error=f"工具 {tool_name} 不存在",
                tool_name=tool_name,
                server_name=self.config.name
            )
        
        try:
            result = await self.session.call_tool(tool_name, arguments)

            # 处理结果内容
            content = ""
            if hasattr(result, 'content'):
                if isinstance(result.content, list):
                    # 如果是内容列表，提取文本
                    content_parts = []
                    for item in result.content:
                        if hasattr(item, 'text'):
                            content_parts.append(item.text)
                        elif isinstance(item, dict) and 'text' in item:
                            content_parts.append(item['text'])
                        else:
                            content_parts.append(str(item))
                    content = "\n".join(content_parts)
                else:
                    content = str(result.content)
            else:
                content = str(result)

            return MCPToolResult(
                success=True,
                content=content,
                tool_name=tool_name,
                server_name=self.config.name
            )
        except Exception as e:
            logger.error(f"调用工具失败 {tool_name}: {e}")
            logger.exception("详细错误信息:")
            return MCPToolResult(
                success=False,
                content="",
                error=str(e),
                tool_name=tool_name,
                server_name=self.config.name
            )
    
    async def disconnect(self):
        """断开连接"""
        # 先关闭会话
        if self.session:
            try:
                await self.session.close()
            except Exception as e:
                logger.error(f"关闭会话失败: {e}")
            finally:
                self.session = None

        # 然后关闭stdio连接
        if self._stdio_context:
            try:
                await self._stdio_context.__aexit__(None, None, None)
            except Exception as e:
                logger.error(f"断开stdio连接失败: {e}")
            finally:
                self._stdio_context = None

        self.connected = False
    
    def get_status(self) -> MCPServerStatus:
        """获取服务器状态"""
        return MCPServerStatus(
            name=self.config.name,
            connected=self.connected,
            tools_count=len(self.tools),
            resources_count=len(self.resources),
            prompts_count=len(self.prompts)
        )


class MCPService:
    """MCP服务管理器"""
    
    def __init__(self):
        self.clients: Dict[str, MCPClient] = {}
        
    async def add_server(self, config: MCPServerConfig):
        """添加MCP服务器"""
        if config.name in self.clients:
            await self.remove_server(config.name)

        client = MCPClient(config)
        self.clients[config.name] = client

        if config.enabled:
            try:
                await client.connect()
                logger.info(f"成功添加并连接MCP服务器: {config.name}")
            except Exception as e:
                logger.error(f"连接MCP服务器失败 {config.name}: {e}")
                # 保留客户端但标记为未连接
        else:
            logger.info(f"添加MCP服务器（未启用）: {config.name}")
    
    async def remove_server(self, server_name: str):
        """移除MCP服务器"""
        if server_name in self.clients:
            await self.clients[server_name].disconnect()
            del self.clients[server_name]
            logger.info(f"移除MCP服务器: {server_name}")
    
    async def call_tool(self, tool_call: MCPToolCall) -> MCPToolResult:
        """调用工具"""
        # 如果指定了服务器名称，直接调用
        if tool_call.server_name and tool_call.server_name in self.clients:
            client = self.clients[tool_call.server_name]
            return await client.call_tool(tool_call.tool_name, tool_call.arguments)
        
        # 否则查找拥有该工具的服务器
        for client in self.clients.values():
            if tool_call.tool_name in client.tools:
                return await client.call_tool(tool_call.tool_name, tool_call.arguments)
        
        return MCPToolResult(
            success=False,
            content="",
            error=f"找不到工具: {tool_call.tool_name}",
            tool_name=tool_call.tool_name,
            server_name=""
        )
    
    def get_all_tools(self) -> List[MCPTool]:
        """获取所有可用工具"""
        tools = []
        for client in self.clients.values():
            if client.connected:
                tools.extend(client.tools.values())
        return tools
    
    def get_all_resources(self) -> List[MCPResource]:
        """获取所有可用资源"""
        resources = []
        for client in self.clients.values():
            if client.connected:
                resources.extend(client.resources.values())
        return resources
    
    def get_all_prompts(self) -> List[MCPPrompt]:
        """获取所有可用提示模板"""
        prompts = []
        for client in self.clients.values():
            if client.connected:
                prompts.extend(client.prompts.values())
        return prompts
    
    def get_server_status(self) -> List[MCPServerStatus]:
        """获取所有服务器状态"""
        return [client.get_status() for client in self.clients.values()]
    
    async def shutdown(self):
        """关闭所有连接"""
        for client in self.clients.values():
            await client.disconnect()
        self.clients.clear()
