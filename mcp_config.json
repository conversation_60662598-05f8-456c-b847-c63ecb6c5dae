{"servers": [{"name": "filesystem", "description": "文件系统操作工具", "transport_type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/code/py/ai-agent"], "env": {}, "url": null, "headers": {}, "enabled": true, "timeout": 30, "retry_count": 3}, {"name": "brave-search", "description": "Brave搜索引擎工具", "transport_type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "your-brave-api-key-here"}, "url": null, "headers": {}, "enabled": false, "timeout": 30, "retry_count": 3}, {"name": "github", "description": "GitHub操作工具", "transport_type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "your-github-token-here"}, "url": null, "headers": {}, "enabled": false, "timeout": 30, "retry_count": 3}, {"name": "sqlite", "description": "SQLite数据库操作工具", "transport_type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite", "/path/to/database.db"], "env": {}, "url": null, "headers": {}, "enabled": false, "timeout": 30, "retry_count": 3}], "global_timeout": 30, "auto_reconnect": true, "log_level": "INFO"}