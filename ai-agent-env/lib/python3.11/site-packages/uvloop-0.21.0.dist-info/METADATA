Metadata-Version: 2.1
Name: uvloop
Version: 0.21.0
Summary: Fast implementation of asyncio event loop on top of libuv
Author-email: <PERSON><PERSON> <<EMAIL>>
License: MIT License
Project-URL: github, https://github.com/MagicStack/uvloop
Keywords: asyncio,networking
Classifier: Development Status :: 5 - Production/Stable
Classifier: Framework :: AsyncIO
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: POSIX
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Topic :: System :: Networking
Requires-Python: >=3.8.0
Description-Content-Type: text/x-rst
License-File: LICENSE-APACHE
License-File: LICENSE-MIT
Provides-Extra: dev
Requires-Dist: setuptools>=60; extra == "dev"
Requires-Dist: Cython~=3.0; extra == "dev"
Provides-Extra: docs
Requires-Dist: Sphinx~=4.1.2; extra == "docs"
Requires-Dist: sphinxcontrib-asyncio~=0.3.0; extra == "docs"
Requires-Dist: sphinx-rtd-theme~=0.5.2; extra == "docs"
Provides-Extra: test
Requires-Dist: aiohttp>=3.10.5; extra == "test"
Requires-Dist: flake8~=5.0; extra == "test"
Requires-Dist: psutil; extra == "test"
Requires-Dist: pycodestyle~=2.9.0; extra == "test"
Requires-Dist: pyOpenSSL~=23.0.0; extra == "test"
Requires-Dist: mypy>=0.800; extra == "test"

.. image:: https://img.shields.io/github/actions/workflow/status/MagicStack/uvloop/tests.yml?branch=master
    :target: https://github.com/MagicStack/uvloop/actions/workflows/tests.yml?query=branch%3Amaster

.. image:: https://img.shields.io/pypi/v/uvloop.svg
    :target: https://pypi.python.org/pypi/uvloop

.. image:: https://pepy.tech/badge/uvloop
    :target: https://pepy.tech/project/uvloop
    :alt: PyPI - Downloads


uvloop is a fast, drop-in replacement of the built-in asyncio
event loop.  uvloop is implemented in Cython and uses libuv
under the hood.

The project documentation can be found
`here <http://uvloop.readthedocs.org/>`_.  Please also check out the
`wiki <https://github.com/MagicStack/uvloop/wiki>`_.


Performance
-----------

uvloop makes asyncio 2-4x faster.

.. image:: https://raw.githubusercontent.com/MagicStack/uvloop/master/performance.png
    :target: http://magic.io/blog/uvloop-blazing-fast-python-networking/

The above chart shows the performance of an echo server with different
message sizes.  The *sockets* benchmark uses ``loop.sock_recv()`` and
``loop.sock_sendall()`` methods; the *streams* benchmark uses asyncio
high-level streams, created by the ``asyncio.start_server()`` function;
and the *protocol* benchmark uses ``loop.create_server()`` with a simple
echo protocol.  Read more about uvloop in a
`blog post <http://magic.io/blog/uvloop-blazing-fast-python-networking/>`_
about it.


Installation
------------

uvloop requires Python 3.8 or greater and is available on PyPI.
Use pip to install it::

    $ pip install uvloop

Note that it is highly recommended to **upgrade pip before** installing
uvloop with::

    $ pip install -U pip


Using uvloop
------------

As of uvloop 0.18, the preferred way of using it is via the
``uvloop.run()`` helper function:


.. code:: python

    import uvloop

    async def main():
        # Main entry-point.
        ...

    uvloop.run(main())

``uvloop.run()`` works by simply configuring ``asyncio.run()``
to use uvloop, passing all of the arguments to it, such as ``debug``,
e.g. ``uvloop.run(main(), debug=True)``.

With Python 3.11 and earlier the following alternative
snippet can be used:

.. code:: python

    import asyncio
    import sys

    import uvloop

    async def main():
        # Main entry-point.
        ...

    if sys.version_info >= (3, 11):
        with asyncio.Runner(loop_factory=uvloop.new_event_loop) as runner:
            runner.run(main())
    else:
        uvloop.install()
        asyncio.run(main())


Building From Source
--------------------

To build uvloop, you'll need Python 3.8 or greater:

1. Clone the repository:

   .. code::

    $ git clone --recursive **************:MagicStack/uvloop.git
    $ cd uvloop

2. Create a virtual environment and activate it:

   .. code::

    $ python3 -m venv uvloop-dev
    $ source uvloop-dev/bin/activate

3. Install development dependencies:

   ..  code::

    $ pip install -e .[dev]

4. Build and run tests:

   .. code::

    $ make
    $ make test


License
-------

uvloop is dual-licensed under MIT and Apache 2.0 licenses.
